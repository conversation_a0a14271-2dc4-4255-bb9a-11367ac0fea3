import type { Zone, Station, Line, System, Equipment, HierarchyTreeNode, BreadcrumbItem } from "@/types/eams";

// Helper function to create breadcrumbs
const createBreadcrumbs = (path: string): BreadcrumbItem[] => {
  const parts = path.split('/');
  const breadcrumbs: BreadcrumbItem[] = [];

  parts.forEach((part, index) => {
    const currentPath = parts.slice(0, index + 1).join('/');
    const level = index === 0 ? 'zone' : index === 1 ? 'station' : index === 2 ? 'line' : 'equipment';
    breadcrumbs.push({
      id: `${level}-${index}`,
      name: part,
      level: level as any,
      path: currentPath
    });
  });

  return breadcrumbs;
};

// Zone A Data Structure
export const zoneA: Zone = {
  id: "ZONE-A",
  name: "Zone A",
  level: "zone",
  path: "Zone A",
  breadcrumbs: createBreadcrumbs("Zone A"),
  description: "Primary operational zone for water pumping systems",
  location: {
    country: "Egypt",
    state: "Cairo",
    city: "New Cairo",
    coordinates: {
      latitude: 30.0444,
      longitude: 31.2357
    }
  },
  assetCount: 32, // Will be calculated dynamically
  stations: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

// Pump Station A01
export const pumpStationA01: Station = {
  id: "STATION-A01",
  name: "Pump Station A01",
  level: "station",
  parentId: "ZONE-A",
  zoneId: "ZONE-A",
  path: "Zone A/Pump Station A01",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01"),
  stationType: "pump_station",
  capacity: 688, // 4 pumps × 172 L/sec = 688 L/sec total
  operationalStatus: "active",
  assetCount: 32,
  lines: [],
  systems: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

// Lines 1-4
export const line1: Line = {
  id: "LINE-A01-L1",
  name: "Line 1",
  level: "line",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Line 1",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 1"),
  lineNumber: 1,
  capacity: 172, // L/sec
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

export const line2: Line = {
  id: "LINE-A01-L2",
  name: "Line 2",
  level: "line",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Line 2",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 2"),
  lineNumber: 2,
  capacity: 172,
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

export const line3: Line = {
  id: "LINE-A01-L3",
  name: "Line 3",
  level: "line",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Line 3",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 3"),
  lineNumber: 3,
  capacity: 172,
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

export const line4: Line = {
  id: "LINE-A01-L4",
  name: "Line 4",
  level: "line",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Line 4",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 4"),
  lineNumber: 4,
  capacity: 172,
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

// Priming System
export const primingSystem: System = {
  id: "SYSTEM-A01-PS",
  name: "Priming System",
  level: "system",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Priming System",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Priming System"),
  systemType: "priming",
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

// Water Hammer System
export const waterHammerSystem: System = {
  id: "SYSTEM-A01-WH",
  name: "Water Hammer System",
  level: "system",
  parentId: "STATION-A01",
  stationId: "STATION-A01",
  path: "Zone A/Pump Station A01/Water Hammer System",
  breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Water Hammer System"),
  systemType: "water_hammer",
  operationalStatus: "active",
  assetCount: 4,
  equipment: [],
  createdAt: "2020-01-15T00:00:00Z",
  updatedAt: "2024-12-15T00:00:00Z"
};

// Equipment for Line 1
export const line1Equipment: Equipment[] = [
  {
    id: "EQ-A01-L1-P1",
    name: "Pump P1",
    level: "equipment",
    parentId: "LINE-A01-L1",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    lineId: "LINE-A01-L1",
    path: "Zone A/Pump Station A01/Line 1/Pump P1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 1/Pump P1"),
    type: "mechanical",
    category: "pump",
    manufacturer: "HMS",
    model: "D200-500A-GGG(485)",
    serialNumber: "9û6",
    assetTag: "P1-A01-L1",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      line: "Line 1",
      building: "Pump House",
      floor: "Ground Floor",
      room: "Pump Room 1"
    },
    specifications: {
      flowRate: 172, // L/sec
      pressure: 80, // m
      ratedPower: 250, // kW (estimated based on motor)
      efficiency: 85
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 35040, // 4 years × 8760 hours
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 2.1,
        peakVelocity: 3.8,
        displacement: 0.025,
        frequency: [30, 60, 90, 120],
        spectrum: [2.1, 1.6, 1.2, 0.8],
        iso10816Zone: "B",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Ahmed"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-L1-M1",
    name: "Motor M1",
    level: "equipment",
    parentId: "LINE-A01-L1",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    lineId: "LINE-A01-L1",
    path: "Zone A/Pump Station A01/Line 1/Motor M1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 1/Motor M1"),
    type: "electrical",
    category: "motor",
    manufacturer: "ELDIN",
    model: "А355SMA4FБT2",
    serialNumber: "200900079",
    assetTag: "M1-A01-L1",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      line: "Line 1",
      building: "Pump House",
      floor: "Ground Floor",
      room: "Motor Room 1"
    },
    specifications: {
      ratedPower: 250, // kW
      ratedVoltage: 415, // V
      speed: 1487, // RPM
      efficiency: 92
    },
    status: "operational",
    condition: "excellent",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 1.8,
        peakVelocity: 3.2,
        displacement: 0.02,
        frequency: [30, 60, 90, 120],
        spectrum: [1.8, 1.4, 1.0, 0.6],
        iso10816Zone: "A",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Ahmed"
      },
      thermography: {
        maxTemperature: 68,
        avgTemperature: 62,
        hotSpots: [],
        baseline: 60,
        deltaT: 8,
        measurementDate: "2024-12-15"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "excellent",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-L1-V1",
    name: "Gate Valve V1",
    level: "equipment",
    parentId: "LINE-A01-L1",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    lineId: "LINE-A01-L1",
    path: "Zone A/Pump Station A01/Line 1/Gate Valve V1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 1/Gate Valve V1"),
    type: "mechanical",
    category: "valve",
    manufacturer: "Placeholder",
    model: "Gate Valve",
    serialNumber: "V-A01-L1-001",
    assetTag: "V1-A01-L1",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      line: "Line 1",
      building: "Pump House",
      floor: "Ground Floor",
      room: "Valve Room 1"
    },
    specifications: {
      diameter: 100, // mm
      pressure: 16, // bar
      temperature: 80 // °C
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-L1-S1",
    name: "Sensor S1",
    level: "equipment",
    parentId: "LINE-A01-L1",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    lineId: "LINE-A01-L1",
    path: "Zone A/Pump Station A01/Line 1/Sensor S1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Line 1/Sensor S1"),
    type: "instrumentation",
    category: "sensor",
    manufacturer: "Placeholder",
    model: "Pressure Sensor",
    serialNumber: "S-A01-L1-001",
    assetTag: "S1-A01-L1",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      line: "Line 1",
      building: "Pump House",
      floor: "Ground Floor",
      room: "Control Room 1"
    },
    specifications: {
      ratedVoltage: 24, // V
      frequency: 50 // Hz
    },
    status: "operational",
    condition: "excellent",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "excellent",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  }
];

// Import equipment from separate file
import { primingSystemEquipment, waterHammerSystemEquipment } from './hierarchicalEquipmentData';

// Helper function to generate equipment for lines 2-4 (similar to line 1)
const generateLineEquipment = (lineNumber: number, lineId: string): Equipment[] => {
  const serialNumbers = {
    2: { pump: "9û7", motor: "200900080" },
    3: { pump: "9û8", motor: "200900081" },
    4: { pump: "9û9", motor: "200900082" }
  };

  const serials = serialNumbers[lineNumber as keyof typeof serialNumbers];

  return [
    // Pump
    {
      id: `EQ-A01-L${lineNumber}-P${lineNumber}`,
      name: `Pump P${lineNumber}`,
      level: "equipment",
      parentId: lineId,
      zoneId: "ZONE-A",
      stationId: "STATION-A01",
      lineId: lineId,
      path: `Zone A/Pump Station A01/Line ${lineNumber}/Pump P${lineNumber}`,
      breadcrumbs: createBreadcrumbs(`Zone A/Pump Station A01/Line ${lineNumber}/Pump P${lineNumber}`),
      type: "mechanical",
      category: "pump",
      manufacturer: "HMS",
      model: "D200-500A-GGG(485)",
      serialNumber: serials.pump,
      assetTag: `P${lineNumber}-A01-L${lineNumber}`,
      location: {
        zone: "Zone A",
        station: "Pump Station A01",
        line: `Line ${lineNumber}`,
        building: "Pump House",
        floor: "Ground Floor",
        room: `Pump Room ${lineNumber}`
      },
      specifications: {
        flowRate: 172,
        pressure: 80,
        ratedPower: 250,
        efficiency: 85
      },
      status: "operational",
      condition: "good",
      installationDate: "2020-01-15",
      operatingHours: 35040,
      conditionMonitoring: {
        vibration: {
          rmsVelocity: 2.0 + Math.random() * 0.4,
          peakVelocity: 3.6 + Math.random() * 0.6,
          displacement: 0.02 + Math.random() * 0.01,
          frequency: [30, 60, 90, 120],
          spectrum: [2.0, 1.5, 1.1, 0.7],
          iso10816Zone: "B",
          measurementDate: "2024-12-15",
          measuredBy: "Technician Ahmed"
        },
        lastUpdated: "2024-12-15",
        overallCondition: "good",
        alerts: []
      },
      failureHistory: [],
      maintenanceHistory: [],
      history: [],
      createdAt: "2020-01-15T00:00:00Z",
      updatedAt: "2024-12-15T00:00:00Z"
    } as Equipment,
    // Motor
    {
      id: `EQ-A01-L${lineNumber}-M${lineNumber}`,
      name: `Motor M${lineNumber}`,
      level: "equipment",
      parentId: lineId,
      zoneId: "ZONE-A",
      stationId: "STATION-A01",
      lineId: lineId,
      path: `Zone A/Pump Station A01/Line ${lineNumber}/Motor M${lineNumber}`,
      breadcrumbs: createBreadcrumbs(`Zone A/Pump Station A01/Line ${lineNumber}/Motor M${lineNumber}`),
      type: "electrical",
      category: "motor",
      manufacturer: "ELDIN",
      model: "А355SMA4FБT2",
      serialNumber: serials.motor,
      assetTag: `M${lineNumber}-A01-L${lineNumber}`,
      location: {
        zone: "Zone A",
        station: "Pump Station A01",
        line: `Line ${lineNumber}`,
        building: "Pump House",
        floor: "Ground Floor",
        room: `Motor Room ${lineNumber}`
      },
      specifications: {
        ratedPower: 250,
        ratedVoltage: 415,
        speed: 1487,
        efficiency: 92
      },
      status: "operational",
      condition: "excellent",
      installationDate: "2020-01-15",
      operatingHours: 35040,
      conditionMonitoring: {
        vibration: {
          rmsVelocity: 1.7 + Math.random() * 0.3,
          peakVelocity: 3.0 + Math.random() * 0.5,
          displacement: 0.018 + Math.random() * 0.008,
          frequency: [30, 60, 90, 120],
          spectrum: [1.8, 1.4, 1.0, 0.6],
          iso10816Zone: "A",
          measurementDate: "2024-12-15",
          measuredBy: "Technician Ahmed"
        },
        thermography: {
          maxTemperature: 65 + Math.random() * 8,
          avgTemperature: 60 + Math.random() * 5,
          hotSpots: [],
          baseline: 60,
          deltaT: 8,
          measurementDate: "2024-12-15"
        },
        lastUpdated: "2024-12-15",
        overallCondition: "excellent",
        alerts: []
      },
      failureHistory: [],
      maintenanceHistory: [],
      history: [],
      createdAt: "2020-01-15T00:00:00Z",
      updatedAt: "2024-12-15T00:00:00Z"
    } as Equipment,
    // Valve
    {
      id: `EQ-A01-L${lineNumber}-V${lineNumber}`,
      name: `Gate Valve V${lineNumber}`,
      level: "equipment",
      parentId: lineId,
      zoneId: "ZONE-A",
      stationId: "STATION-A01",
      lineId: lineId,
      path: `Zone A/Pump Station A01/Line ${lineNumber}/Gate Valve V${lineNumber}`,
      breadcrumbs: createBreadcrumbs(`Zone A/Pump Station A01/Line ${lineNumber}/Gate Valve V${lineNumber}`),
      type: "mechanical",
      category: "valve",
      manufacturer: "Placeholder",
      model: "Gate Valve",
      serialNumber: `V-A01-L${lineNumber}-001`,
      assetTag: `V${lineNumber}-A01-L${lineNumber}`,
      location: {
        zone: "Zone A",
        station: "Pump Station A01",
        line: `Line ${lineNumber}`,
        building: "Pump House",
        floor: "Ground Floor",
        room: `Valve Room ${lineNumber}`
      },
      specifications: {
        diameter: 100,
        pressure: 16,
        temperature: 80
      },
      status: "operational",
      condition: "good",
      installationDate: "2020-01-15",
      operatingHours: 35040,
      conditionMonitoring: {
        lastUpdated: "2024-12-15",
        overallCondition: "good",
        alerts: []
      },
      failureHistory: [],
      maintenanceHistory: [],
      history: [],
      createdAt: "2020-01-15T00:00:00Z",
      updatedAt: "2024-12-15T00:00:00Z"
    } as Equipment,
    // Sensor
    {
      id: `EQ-A01-L${lineNumber}-S${lineNumber}`,
      name: `Sensor S${lineNumber}`,
      level: "equipment",
      parentId: lineId,
      zoneId: "ZONE-A",
      stationId: "STATION-A01",
      lineId: lineId,
      path: `Zone A/Pump Station A01/Line ${lineNumber}/Sensor S${lineNumber}`,
      breadcrumbs: createBreadcrumbs(`Zone A/Pump Station A01/Line ${lineNumber}/Sensor S${lineNumber}`),
      type: "instrumentation",
      category: "sensor",
      manufacturer: "Placeholder",
      model: "Pressure Sensor",
      serialNumber: `S-A01-L${lineNumber}-001`,
      assetTag: `S${lineNumber}-A01-L${lineNumber}`,
      location: {
        zone: "Zone A",
        station: "Pump Station A01",
        line: `Line ${lineNumber}`,
        building: "Pump House",
        floor: "Ground Floor",
        room: `Control Room ${lineNumber}`
      },
      specifications: {
        ratedVoltage: 24,
        frequency: 50
      },
      status: "operational",
      condition: "excellent",
      installationDate: "2020-01-15",
      operatingHours: 35040,
      conditionMonitoring: {
        lastUpdated: "2024-12-15",
        overallCondition: "excellent",
        alerts: []
      },
      failureHistory: [],
      maintenanceHistory: [],
      history: [],
      createdAt: "2020-01-15T00:00:00Z",
      updatedAt: "2024-12-15T00:00:00Z"
    } as Equipment
  ];
};

// Generate equipment for lines 2-4
export const line2Equipment = generateLineEquipment(2, "LINE-A01-L2");
export const line3Equipment = generateLineEquipment(3, "LINE-A01-L3");
export const line4Equipment = generateLineEquipment(4, "LINE-A01-L4");

// Assign equipment to lines and systems
line1.equipment = line1Equipment;
line2.equipment = line2Equipment;
line3.equipment = line3Equipment;
line4.equipment = line4Equipment;
primingSystem.equipment = primingSystemEquipment;
waterHammerSystem.equipment = waterHammerSystemEquipment;

// Assign lines and systems to station
pumpStationA01.lines = [line1, line2, line3, line4];
pumpStationA01.systems = [primingSystem, waterHammerSystem];

// Assign station to zone
zoneA.stations = [pumpStationA01];

// Flatten all equipment for easy access
export const allHierarchicalEquipment: Equipment[] = [
  ...line1Equipment,
  ...line2Equipment,
  ...line3Equipment,
  ...line4Equipment,
  ...primingSystemEquipment,
  ...waterHammerSystemEquipment
];

// Export the complete hierarchical structure
export const hierarchicalAssetStructure = {
  zones: [zoneA],
  allEquipment: allHierarchicalEquipment
};

// Backward compatibility - alias for existing code
export const industrialAssets = allHierarchicalEquipment;
