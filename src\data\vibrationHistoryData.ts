// Vibration history data structure for logging all vibration form submissions
import { allHierarchicalEquipment } from './hierarchicalAssetData';

export interface VibrationHistoryRecord {
    id: string;
    equipmentId: string;
    date: string;
    pumpData: any; // Store all pump fields from the form
    motorData: any; // Store all motor fields from the form
    positions: any; // Store all positions fields from the form
    zone: string;
    pumpNo: string;
    motorBrand: string;
    serialNumbers: string;
    project: string;
    pumpStation: string;
    pumpBrand: string;
    operationHr: string;
    operationPower: string;
    pumpHead: string;
    pumpFlowRate: string;
    dischargeP: string;
    mainHeaderP: string;
    suctionP: string;
    fatPumpPower: string;
    ratedMotorPower: string;
    enteredBy?: string;
    // All vibration readings for pump and motor, NDE and DE
    pumpNDE_bv: string;
    pumpNDE_bg: string;
    pumpNDE_accV: string;
    pumpNDE_accH: string;
    pumpNDE_accAxl: string;
    pumpNDE_velV: string;
    pumpNDE_velH: string;
    pumpNDE_velAxl: string;
    pumpNDE_temp: string;
    pumpDE_bv: string;
    pumpDE_bg: string;
    pumpDE_accV: string;
    pumpDE_accH: string;
    pumpDE_accAxl: string;
    pumpDE_velV: string;
    pumpDE_velH: string;
    pumpDE_velAxl: string;
    pumpDE_temp: string;
    motorNDE_bv: string;
    motorNDE_bg: string;
    motorNDE_accV: string;
    motorNDE_accH: string;
    motorNDE_accAxl: string;
    motorNDE_velV: string;
    motorNDE_velH: string;
    motorNDE_velAxl: string;
    motorNDE_temp: string;
    motorDE_bv: string;
    motorDE_bg: string;
    motorDE_accV: string;
    motorDE_accH: string;
    motorDE_accAxl: string;
    motorDE_velV: string;
    motorDE_velH: string;
    motorDE_velAxl: string;
    motorDE_temp: string;
    vibrationRMS: number;
}

// Generate realistic vibration data for all hierarchical equipment
const generateVibrationHistoryData = (): VibrationHistoryRecord[] => {
    const records: VibrationHistoryRecord[] = [];
    const months = [
        '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12',
        '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06',
    ];

    // Filter equipment that can have vibration monitoring (rotating equipment)
    const monitorableEquipment = allHierarchicalEquipment.filter(eq =>
        ['pump', 'motor', 'compressor'].includes(eq.category)
    );

    // Equipment-specific vibration patterns based on type and manufacturer
    const getVibrationPattern = (equipment: any) => {
        const basePattern = [1.2, 2.8, 4.5, 7.0, 9.5, 12.2, 14.8, 10.5, 6.0, 3.5, 2.0, 1.0];

        // Adjust pattern based on equipment category
        let multiplier = 1.0;
        switch (equipment.category) {
            case 'pump':
                multiplier = equipment.name.includes('Priming') ? 0.8 : 1.0; // Priming pumps typically have lower vibration
                break;
            case 'motor':
                multiplier = 0.7; // Motors typically have lower vibration than pumps
                break;
            case 'compressor':
                multiplier = 1.3; // Compressors typically have higher vibration
                break;
        }

        // Adjust based on manufacturer quality
        const manufacturerMultipliers: { [key: string]: number } = {
            'HMS': 0.9,
            'SAER': 0.9,
            'ELDIN': 1.0,
            'ABB': 0.8,
            'ROBUSCHI/Italy': 0.85,
            'El Haggar Misr': 1.1
        };

        const manufacturerMultiplier = manufacturerMultipliers[equipment.manufacturer] || 1.0;

        return basePattern.map(val => val * multiplier * manufacturerMultiplier);
    };

    // Generate realistic vibration readings based on equipment specifications
    const generateVibrationReading = (baseValue: number, variation: number = 0.2) => {
        return (baseValue + (Math.random() - 0.5) * variation).toFixed(2);
    };

    monitorableEquipment.slice(0, 50).forEach((equipment, eqIdx) => { // Limit to first 50 for performance
        const vibrationPattern = getVibrationPattern(equipment);

        months.forEach((month, mIdx) => {
            const date = `${month}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`;
            const vibrationRMS = vibrationPattern[mIdx % vibrationPattern.length] + (Math.random() - 0.5) * 0.5;

            const pumpBaseVibration = equipment.category === 'pump' ? vibrationRMS : vibrationRMS * 0.8;
            const motorBaseVibration = equipment.category === 'motor' ? vibrationRMS : vibrationRMS * 0.7;

            records.push({
                id: `VH-${equipment.id}-${month}`,
                equipmentId: equipment.id,
                date,
                pumpData: {
                    nde: {
                        bv: generateVibrationReading(pumpBaseVibration),
                        bg: generateVibrationReading(pumpBaseVibration * 0.9),
                        accV: generateVibrationReading(pumpBaseVibration * 2.5),
                        accH: generateVibrationReading(pumpBaseVibration * 2.3),
                        accAxl: generateVibrationReading(pumpBaseVibration * 1.8),
                        velV: generateVibrationReading(pumpBaseVibration),
                        velH: generateVibrationReading(pumpBaseVibration * 0.95),
                        velAxl: generateVibrationReading(pumpBaseVibration * 0.85),
                        temp: generateVibrationReading(45, 10)
                    },
                    de: {
                        bv: generateVibrationReading(pumpBaseVibration * 1.1),
                        bg: generateVibrationReading(pumpBaseVibration),
                        accV: generateVibrationReading(pumpBaseVibration * 2.7),
                        accH: generateVibrationReading(pumpBaseVibration * 2.5),
                        accAxl: generateVibrationReading(pumpBaseVibration * 2.0),
                        velV: generateVibrationReading(pumpBaseVibration * 1.05),
                        velH: generateVibrationReading(pumpBaseVibration),
                        velAxl: generateVibrationReading(pumpBaseVibration * 0.9),
                        temp: generateVibrationReading(47, 10)
                    }
                },
                motorData: {
                    nde: {
                        bv: generateVibrationReading(motorBaseVibration),
                        bg: generateVibrationReading(motorBaseVibration * 0.9),
                        accV: generateVibrationReading(motorBaseVibration * 2.0),
                        accH: generateVibrationReading(motorBaseVibration * 1.9),
                        accAxl: generateVibrationReading(motorBaseVibration * 1.5),
                        velV: generateVibrationReading(motorBaseVibration),
                        velH: generateVibrationReading(motorBaseVibration * 0.95),
                        velAxl: generateVibrationReading(motorBaseVibration * 0.8),
                        temp: generateVibrationReading(42, 8)
                    },
                    de: {
                        bv: generateVibrationReading(motorBaseVibration * 1.05),
                        bg: generateVibrationReading(motorBaseVibration * 0.95),
                        accV: generateVibrationReading(motorBaseVibration * 2.1),
                        accH: generateVibrationReading(motorBaseVibration * 2.0),
                        accAxl: generateVibrationReading(motorBaseVibration * 1.6),
                        velV: generateVibrationReading(motorBaseVibration * 1.02),
                        velH: generateVibrationReading(motorBaseVibration * 0.98),
                        velAxl: generateVibrationReading(motorBaseVibration * 0.85),
                        temp: generateVibrationReading(44, 8)
                    }
                },
                positions: {
                    pLeg1: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    pLeg2: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    pLeg3: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    pLeg4: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    mLeg1: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    mLeg2: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    mLeg3: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    mLeg4: vibrationRMS < 3 ? 'OK' : vibrationRMS < 6 ? 'Warning' : 'Critical',
                    vibrationRMS
                },
                vibrationRMS,
                zone: equipment.location?.zone || 'A',
                pumpNo: equipment.assetTag || equipment.name,
                motorBrand: equipment.category === 'motor' ? equipment.manufacturer : 'Associated Motor',
                serialNumbers: equipment.serialNumber || `SN-${equipment.id}`,
                project: 'Toshka Water Project',
                pumpStation: equipment.location?.station || 'Unknown Station',
                pumpBrand: equipment.category === 'pump' ? equipment.manufacturer : 'Associated Pump',
                operationHr: equipment.operatingHours?.toString() || '8760',
                operationPower: equipment.specifications?.ratedPower?.toString() || '75',
                pumpHead: equipment.specifications?.head?.toString() || '50',
                pumpFlowRate: equipment.specifications?.flowRate?.toString() || '100',
                dischargeP: equipment.specifications?.pressure?.toString() || '5.5',
                mainHeaderP: '5.0',
                suctionP: '0.5',
                fatPumpPower: equipment.specifications?.ratedPower?.toString() || '75',
                ratedMotorPower: equipment.specifications?.ratedPower?.toString() || '75',
                enteredBy: 'System Generated',
                // Detailed vibration readings
                pumpNDE_bv: generateVibrationReading(pumpBaseVibration),
                pumpNDE_bg: generateVibrationReading(pumpBaseVibration * 0.9),
                pumpNDE_accV: generateVibrationReading(pumpBaseVibration * 2.5),
                pumpNDE_accH: generateVibrationReading(pumpBaseVibration * 2.3),
                pumpNDE_accAxl: generateVibrationReading(pumpBaseVibration * 1.8),
                pumpNDE_velV: generateVibrationReading(pumpBaseVibration),
                pumpNDE_velH: generateVibrationReading(pumpBaseVibration * 0.95),
                pumpNDE_velAxl: generateVibrationReading(pumpBaseVibration * 0.85),
                pumpNDE_temp: generateVibrationReading(45, 10),
                pumpDE_bv: generateVibrationReading(pumpBaseVibration * 1.1),
                pumpDE_bg: generateVibrationReading(pumpBaseVibration),
                pumpDE_accV: generateVibrationReading(pumpBaseVibration * 2.7),
                pumpDE_accH: generateVibrationReading(pumpBaseVibration * 2.5),
                pumpDE_accAxl: generateVibrationReading(pumpBaseVibration * 2.0),
                pumpDE_velV: generateVibrationReading(pumpBaseVibration * 1.05),
                pumpDE_velH: generateVibrationReading(pumpBaseVibration),
                pumpDE_velAxl: generateVibrationReading(pumpBaseVibration * 0.9),
                pumpDE_temp: generateVibrationReading(47, 10),
                motorNDE_bv: generateVibrationReading(motorBaseVibration),
                motorNDE_bg: generateVibrationReading(motorBaseVibration * 0.9),
                motorNDE_accV: generateVibrationReading(motorBaseVibration * 2.0),
                motorNDE_accH: generateVibrationReading(motorBaseVibration * 1.9),
                motorNDE_accAxl: generateVibrationReading(motorBaseVibration * 1.5),
                motorNDE_velV: generateVibrationReading(motorBaseVibration),
                motorNDE_velH: generateVibrationReading(motorBaseVibration * 0.95),
                motorNDE_velAxl: generateVibrationReading(motorBaseVibration * 0.8),
                motorNDE_temp: generateVibrationReading(42, 8),
                motorDE_bv: generateVibrationReading(motorBaseVibration * 1.05),
                motorDE_bg: generateVibrationReading(motorBaseVibration * 0.95),
                motorDE_accV: generateVibrationReading(motorBaseVibration * 2.1),
                motorDE_accH: generateVibrationReading(motorBaseVibration * 2.0),
                motorDE_accAxl: generateVibrationReading(motorBaseVibration * 1.6),
                motorDE_velV: generateVibrationReading(motorBaseVibration * 1.02),
                motorDE_velH: generateVibrationReading(motorBaseVibration * 0.98),
                motorDE_velAxl: generateVibrationReading(motorBaseVibration * 0.85),
                motorDE_temp: generateVibrationReading(44, 8),
            });
        });
    });

    return records;
};

export const exampleVibrationHistory: VibrationHistoryRecord[] = generateVibrationHistoryData();

export const initialVibrationHistory: VibrationHistoryRecord[] = exampleVibrationHistory; 