import type { Equipment, Zone, Station, Line, System, HierarchyStats } from "@/types/eams";

// Equipment-level KPI calculations
export interface EquipmentKPIs {
  healthScore: number;
  availability: number;
  reliability: number;
  efficiency: number;
  mtbf: number; // Mean Time Between Failures
  mttr: number; // Mean Time To Repair
  utilizationRate: number;
  energyEfficiency: number;
  maintenanceCost: number;
  downtimeHours: number;
  criticalAlerts: number;
  vibrationScore: number;
  temperatureScore: number;
}

// Hierarchy-level KPI calculations
export interface HierarchyKPIs {
  totalEquipment: number;
  operationalEquipment: number;
  maintenanceEquipment: number;
  faultEquipment: number;
  avgHealthScore: number;
  avgAvailability: number;
  avgReliability: number;
  avgEfficiency: number;
  totalMaintenanceCost: number;
  totalDowntimeHours: number;
  criticalAlerts: number;
  oee: number; // Overall Equipment Effectiveness
}

// Calculate equipment-level KPIs
export const calculateEquipmentKPIs = (equipment: Equipment): EquipmentKPIs => {
  const operatingHours = equipment.operatingHours || 0;
  const installationDate = equipment.installationDate ? new Date(equipment.installationDate) : new Date();
  const daysSinceInstallation = Math.max(1, (Date.now() - installationDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Health Score based on condition and monitoring data
  let healthScore = 100;
  
  // Condition impact
  switch (equipment.condition) {
    case 'excellent': healthScore = 95; break;
    case 'good': healthScore = 80; break;
    case 'fair': healthScore = 65; break;
    case 'poor': healthScore = 40; break;
    case 'critical': healthScore = 20; break;
    default: healthScore = 50;
  }
  
  // Vibration impact
  let vibrationScore = 100;
  if (equipment.conditionMonitoring?.vibration) {
    const rms = equipment.conditionMonitoring.vibration.rmsVelocity;
    const zone = equipment.conditionMonitoring.vibration.iso10816Zone;
    
    switch (zone) {
      case 'A': vibrationScore = 95; break;
      case 'B': vibrationScore = 80; break;
      case 'C': vibrationScore = 60; break;
      case 'D': vibrationScore = 30; break;
      default: vibrationScore = 70;
    }
    
    // Adjust health score based on vibration
    healthScore = Math.min(healthScore, vibrationScore);
  }
  
  // Temperature impact
  let temperatureScore = 100;
  if (equipment.conditionMonitoring?.thermography) {
    const deltaT = equipment.conditionMonitoring.thermography.deltaT;
    if (deltaT > 20) temperatureScore = 60;
    else if (deltaT > 15) temperatureScore = 75;
    else if (deltaT > 10) temperatureScore = 85;
    else temperatureScore = 95;
    
    // Adjust health score based on temperature
    healthScore = Math.min(healthScore, temperatureScore);
  }
  
  // Alerts impact
  const criticalAlerts = equipment.conditionMonitoring?.alerts?.filter(a => a.severity === 'critical').length || 0;
  const warningAlerts = equipment.conditionMonitoring?.alerts?.filter(a => a.severity === 'warning').length || 0;
  healthScore -= (criticalAlerts * 10) + (warningAlerts * 5);
  healthScore = Math.max(0, healthScore);
  
  // Availability calculation (based on status and downtime)
  let availability = 100;
  switch (equipment.status) {
    case 'operational': availability = 98; break;
    case 'maintenance': availability = 0; break;
    case 'fault': availability = 0; break;
    case 'offline': availability = 0; break;
    case 'testing': availability = 50; break;
    default: availability = 80;
  }
  
  // Reliability calculation (based on failure history and age)
  const failureCount = equipment.failureHistory?.length || 0;
  const reliability = Math.max(60, 100 - (failureCount * 5) - (daysSinceInstallation / 365 * 2));
  
  // Efficiency calculation (based on specifications and condition)
  const specEfficiency = equipment.specifications?.efficiency || 85;
  const conditionFactor = healthScore / 100;
  const efficiency = specEfficiency * conditionFactor;
  
  // MTBF calculation (Mean Time Between Failures)
  const mtbf = failureCount > 0 ? operatingHours / failureCount : Math.min(8760, operatingHours);
  
  // MTTR calculation (Mean Time To Repair)
  const maintenanceCount = equipment.maintenanceHistory?.length || 0;
  const mttr = maintenanceCount > 0 ? (maintenanceCount * 4) / maintenanceCount : 4; // Average 4 hours
  
  // Utilization rate (based on operating hours vs expected)
  const expectedHours = daysSinceInstallation * 24 * 0.8; // 80% expected utilization
  const utilizationRate = Math.min(100, (operatingHours / expectedHours) * 100);
  
  // Energy efficiency (based on power rating and condition)
  const energyEfficiency = efficiency * 0.9; // Approximate correlation
  
  // Maintenance cost (estimated based on history and condition)
  const baseCost = (equipment.specifications?.ratedPower || 50) * 20; // $20 per kW annually
  const conditionMultiplier = equipment.condition === 'poor' ? 2 : equipment.condition === 'critical' ? 3 : 1;
  const maintenanceCost = baseCost * conditionMultiplier;
  
  // Downtime hours (estimated based on maintenance and failures)
  const downtimeHours = (failureCount * 8) + (maintenanceCount * 4);
  
  return {
    healthScore: Math.round(healthScore),
    availability: Math.round(availability * 100) / 100,
    reliability: Math.round(reliability * 100) / 100,
    efficiency: Math.round(efficiency * 100) / 100,
    mtbf: Math.round(mtbf),
    mttr: Math.round(mttr * 100) / 100,
    utilizationRate: Math.round(utilizationRate * 100) / 100,
    energyEfficiency: Math.round(energyEfficiency * 100) / 100,
    maintenanceCost: Math.round(maintenanceCost),
    downtimeHours: Math.round(downtimeHours),
    criticalAlerts,
    vibrationScore: Math.round(vibrationScore),
    temperatureScore: Math.round(temperatureScore)
  };
};

// Calculate hierarchy-level KPIs (roll-up from equipment)
export const calculateHierarchyKPIs = (equipment: Equipment[]): HierarchyKPIs => {
  if (equipment.length === 0) {
    return {
      totalEquipment: 0,
      operationalEquipment: 0,
      maintenanceEquipment: 0,
      faultEquipment: 0,
      avgHealthScore: 0,
      avgAvailability: 0,
      avgReliability: 0,
      avgEfficiency: 0,
      totalMaintenanceCost: 0,
      totalDowntimeHours: 0,
      criticalAlerts: 0,
      oee: 0
    };
  }
  
  const equipmentKPIs = equipment.map(eq => calculateEquipmentKPIs(eq));
  
  const operationalEquipment = equipment.filter(eq => eq.status === 'operational').length;
  const maintenanceEquipment = equipment.filter(eq => eq.status === 'maintenance').length;
  const faultEquipment = equipment.filter(eq => eq.status === 'fault').length;
  
  const avgHealthScore = equipmentKPIs.reduce((sum, kpi) => sum + kpi.healthScore, 0) / equipment.length;
  const avgAvailability = equipmentKPIs.reduce((sum, kpi) => sum + kpi.availability, 0) / equipment.length;
  const avgReliability = equipmentKPIs.reduce((sum, kpi) => sum + kpi.reliability, 0) / equipment.length;
  const avgEfficiency = equipmentKPIs.reduce((sum, kpi) => sum + kpi.efficiency, 0) / equipment.length;
  
  const totalMaintenanceCost = equipmentKPIs.reduce((sum, kpi) => sum + kpi.maintenanceCost, 0);
  const totalDowntimeHours = equipmentKPIs.reduce((sum, kpi) => sum + kpi.downtimeHours, 0);
  const criticalAlerts = equipmentKPIs.reduce((sum, kpi) => sum + kpi.criticalAlerts, 0);
  
  // OEE calculation (Availability × Performance × Quality)
  const performance = avgEfficiency / 100; // Assuming efficiency represents performance
  const quality = Math.max(0.8, 1 - (criticalAlerts / equipment.length * 0.1)); // Quality based on alerts
  const oee = (avgAvailability / 100) * performance * quality * 100;
  
  return {
    totalEquipment: equipment.length,
    operationalEquipment,
    maintenanceEquipment,
    faultEquipment,
    avgHealthScore: Math.round(avgHealthScore),
    avgAvailability: Math.round(avgAvailability * 100) / 100,
    avgReliability: Math.round(avgReliability * 100) / 100,
    avgEfficiency: Math.round(avgEfficiency * 100) / 100,
    totalMaintenanceCost: Math.round(totalMaintenanceCost),
    totalDowntimeHours: Math.round(totalDowntimeHours),
    criticalAlerts,
    oee: Math.round(oee * 100) / 100
  };
};

// Calculate zone-level KPIs
export const calculateZoneKPIs = (zone: Zone): HierarchyKPIs => {
  const allEquipment: Equipment[] = [];
  
  zone.stations.forEach(station => {
    station.lines.forEach(line => {
      allEquipment.push(...line.equipment);
    });
    station.systems.forEach(system => {
      allEquipment.push(...system.equipment);
    });
  });
  
  return calculateHierarchyKPIs(allEquipment);
};

// Calculate station-level KPIs
export const calculateStationKPIs = (station: Station): HierarchyKPIs => {
  const allEquipment: Equipment[] = [];
  
  station.lines.forEach(line => {
    allEquipment.push(...line.equipment);
  });
  station.systems.forEach(system => {
    allEquipment.push(...system.equipment);
  });
  
  return calculateHierarchyKPIs(allEquipment);
};

// Calculate line-level KPIs
export const calculateLineKPIs = (line: Line): HierarchyKPIs => {
  return calculateHierarchyKPIs(line.equipment);
};

// Calculate system-level KPIs
export const calculateSystemKPIs = (system: System): HierarchyKPIs => {
  return calculateHierarchyKPIs(system.equipment);
};

// Calculate overall hierarchy statistics
export const calculateOverallStats = (zones: Zone[]): HierarchyStats => {
  let totalStations = 0;
  let totalLines = 0;
  let totalSystems = 0;
  let totalEquipment = 0;
  let operationalEquipment = 0;
  let maintenanceEquipment = 0;
  let faultEquipment = 0;
  let criticalAlerts = 0;
  
  zones.forEach(zone => {
    totalStations += zone.stations.length;
    
    zone.stations.forEach(station => {
      totalLines += station.lines.length;
      totalSystems += station.systems.length;
      
      station.lines.forEach(line => {
        totalEquipment += line.equipment.length;
        line.equipment.forEach(eq => {
          if (eq.status === 'operational') operationalEquipment++;
          else if (eq.status === 'maintenance') maintenanceEquipment++;
          else if (eq.status === 'fault') faultEquipment++;
          
          criticalAlerts += eq.conditionMonitoring?.alerts?.filter(a => a.severity === 'critical').length || 0;
        });
      });
      
      station.systems.forEach(system => {
        totalEquipment += system.equipment.length;
        system.equipment.forEach(eq => {
          if (eq.status === 'operational') operationalEquipment++;
          else if (eq.status === 'maintenance') maintenanceEquipment++;
          else if (eq.status === 'fault') faultEquipment++;
          
          criticalAlerts += eq.conditionMonitoring?.alerts?.filter(a => a.severity === 'critical').length || 0;
        });
      });
    });
  });
  
  return {
    totalZones: zones.length,
    totalStations,
    totalLines,
    totalSystems,
    totalEquipment,
    operationalEquipment,
    maintenanceEquipment,
    faultEquipment,
    criticalAlerts
  };
};
