import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
    CalendarIcon,
    Activity,
    Thermometer,
    Zap,
    Settings,
    TrendingUp,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Plus,
    ChevronRight,
    ChevronLeft,
    MapPin,
    Gauge,
    BarChart3,
    Waves,
    Factory,
    Cog,
    Shield,
    Target,
    Cpu,
    Database,
    Search,
    Filter,
    Eye,
    Download,
    Upload
} from 'lucide-react';
import { useAssetContext } from '@/contexts/AssetContext';
import { useToast } from '@/hooks/use-toast';
import { useThemeColors } from '@/hooks/use-theme-colors';
import type { VibrationHistoryRecord } from '@/data/vibrationHistoryData';
import { allHierarchicalEquipment, zoneA } from '@/data/hierarchicalAssetData';

interface EnhancedVibrationFormProps {
    open: boolean;
    onClose: () => void;
    record?: VibrationHistoryRecord | null;
    readOnly?: boolean;
}

// Multi-step wizard steps
const FORM_STEPS = [
    { id: 'equipment', title: 'Equipment Selection', icon: Factory, description: 'Select equipment for vibration monitoring' },
    { id: 'operational', title: 'Operational Data', icon: Gauge, description: 'Enter operational parameters' },
    { id: 'vibration', title: 'Vibration Measurements', icon: Waves, description: 'Record vibration readings' },
    { id: 'analysis', title: 'Analysis & Review', icon: BarChart3, description: 'Review and analyze data' }
];

// Equipment categories with enhanced metadata
const EQUIPMENT_CATEGORIES = {
    pump: {
        label: 'Pumps',
        icon: Cpu,
        color: 'bg-blue-500',
        description: 'Centrifugal and positive displacement pumps',
        vibrationLimits: { good: 2.8, acceptable: 7.1, unacceptable: 18.0 }
    },
    motor: {
        label: 'Motors',
        icon: Zap,
        color: 'bg-green-500',
        description: 'Electric motors and drives',
        vibrationLimits: { good: 1.8, acceptable: 4.5, unacceptable: 11.0 }
    },
    compressor: {
        label: 'Compressors',
        icon: Settings,
        color: 'bg-purple-500',
        description: 'Air and gas compressors',
        vibrationLimits: { good: 3.5, acceptable: 8.8, unacceptable: 22.0 }
    }
};

// Generate enhanced equipment options
const generateEquipmentOptions = () => {
    return allHierarchicalEquipment
        .filter(eq => ['pump', 'motor', 'compressor'].includes(eq.category))
        .map(equipment => ({
            id: equipment.id,
            name: equipment.name,
            manufacturer: equipment.manufacturer,
            model: equipment.model,
            assetTag: equipment.assetTag,
            category: equipment.category,
            location: equipment.location,
            serialNumber: equipment.serialNumber,
            specifications: equipment.specifications,
            hierarchicalPath: `${equipment.location?.zone || 'Zone A'} → ${equipment.location?.station || 'Unknown'} → ${equipment.location?.line || equipment.location?.system || 'Equipment'}`,
            categoryInfo: EQUIPMENT_CATEGORIES[equipment.category as keyof typeof EQUIPMENT_CATEGORIES]
        }));
};

const equipmentOptions = generateEquipmentOptions();

// Enhanced form structure
const initialFormData = {
    // Step 1: Equipment Selection
    selectedEquipment: '',
    equipmentDetails: null as any,

    // Step 2: Operational Data
    date: format(new Date(), 'yyyy-MM-dd'),
    operatingHours: '',
    operatingPower: '',
    operatingSpeed: '',
    operatingTemperature: '',
    operatingPressure: '',
    operatingFlow: '',

    // Step 3: Vibration Measurements
    vibrationData: {
        pump: {
            nde: { velocity: '', acceleration: '', displacement: '', temperature: '' },
            de: { velocity: '', acceleration: '', displacement: '', temperature: '' }
        },
        motor: {
            nde: { velocity: '', acceleration: '', displacement: '', temperature: '' },
            de: { velocity: '', acceleration: '', displacement: '', temperature: '' }
        }
    },

    // Step 4: Analysis
    overallCondition: '',
    recommendations: '',
    nextInspectionDate: '',
    notes: ''
};

const EnhancedVibrationForm: React.FC<EnhancedVibrationFormProps> = ({
    open,
    onClose,
    record,
    readOnly = false
}) => {
    const { addVibrationHistoryEntry, triggerDataUpdate } = useAssetContext();
    const { toast } = useToast();
    const { getThemeClasses } = useThemeColors();
    const themeClasses = getThemeClasses();

    const [currentStep, setCurrentStep] = useState(0);
    const [formProgress, setFormProgress] = useState(0);
    const [selectedEquipment, setSelectedEquipment] = useState<any>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [categoryFilter, setCategoryFilter] = useState('all');

    const methods = useForm({ defaultValues: initialFormData });
    const { control, handleSubmit, watch, setValue, formState } = methods;
    const { errors } = formState;
    const formValues = watch();

    // Calculate form progress
    useEffect(() => {
        const totalSteps = FORM_STEPS.length;
        const progress = ((currentStep + 1) / totalSteps) * 100;
        setFormProgress(progress);
    }, [currentStep]);

    // Filter equipment based on search and category
    const filteredEquipment = equipmentOptions.filter(equipment => {
        const matchesSearch = equipment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            equipment.manufacturer.toLowerCase().includes(searchTerm.toLowerCase()) ||
            equipment.assetTag.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = categoryFilter === 'all' || equipment.category === categoryFilter;
        return matchesSearch && matchesCategory;
    });

    // Handle equipment selection
    const handleEquipmentSelect = (equipmentId: string) => {
        const equipment = equipmentOptions.find(eq => eq.id === equipmentId);
        if (equipment) {
            setSelectedEquipment(equipment);
            setValue('selectedEquipment', equipmentId);
            setValue('equipmentDetails', equipment);
        }
    };

    // Navigation functions
    const nextStep = () => {
        if (currentStep < FORM_STEPS.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    // Form submission
    const onSubmit = (data: any) => {
        const vibrationRecord: VibrationHistoryRecord = {
            id: `VH-${Date.now()}`,
            equipmentId: data.selectedEquipment,
            date: data.date,
            pumpData: data.vibrationData.pump,
            motorData: data.vibrationData.motor,
            positions: {},
            zone: selectedEquipment?.location?.zone || 'A',
            pumpNo: selectedEquipment?.assetTag || '',
            motorBrand: selectedEquipment?.manufacturer || '',
            serialNumbers: selectedEquipment?.serialNumber || '',
            project: 'Toshka Water Project',
            pumpStation: selectedEquipment?.location?.station || '',
            pumpBrand: selectedEquipment?.manufacturer || '',
            operationHr: data.operatingHours,
            operationPower: data.operatingPower,
            pumpHead: selectedEquipment?.specifications?.head?.toString() || '',
            pumpFlowRate: selectedEquipment?.specifications?.flowRate?.toString() || '',
            dischargeP: data.operatingPressure,
            mainHeaderP: '5.0',
            suctionP: '0.5',
            fatPumpPower: data.operatingPower,
            ratedMotorPower: selectedEquipment?.specifications?.ratedPower?.toString() || '',
            enteredBy: 'Current User',
            vibrationRMS: parseFloat(data.vibrationData.pump.nde.velocity) || 0,
            // Add all required vibration fields
            pumpNDE_bv: data.vibrationData.pump.nde.velocity,
            pumpNDE_bg: '0',
            pumpNDE_accV: data.vibrationData.pump.nde.acceleration,
            pumpNDE_accH: '0',
            pumpNDE_accAxl: '0',
            pumpNDE_velV: data.vibrationData.pump.nde.velocity,
            pumpNDE_velH: '0',
            pumpNDE_velAxl: '0',
            pumpNDE_temp: data.vibrationData.pump.nde.temperature,
            pumpDE_bv: data.vibrationData.pump.de.velocity,
            pumpDE_bg: '0',
            pumpDE_accV: data.vibrationData.pump.de.acceleration,
            pumpDE_accH: '0',
            pumpDE_accAxl: '0',
            pumpDE_velV: data.vibrationData.pump.de.velocity,
            pumpDE_velH: '0',
            pumpDE_velAxl: '0',
            pumpDE_temp: data.vibrationData.pump.de.temperature,
            motorNDE_bv: data.vibrationData.motor.nde.velocity,
            motorNDE_bg: '0',
            motorNDE_accV: data.vibrationData.motor.nde.acceleration,
            motorNDE_accH: '0',
            motorNDE_accAxl: '0',
            motorNDE_velV: data.vibrationData.motor.nde.velocity,
            motorNDE_velH: '0',
            motorNDE_velAxl: '0',
            motorNDE_temp: data.vibrationData.motor.nde.temperature,
            motorDE_bv: data.vibrationData.motor.de.velocity,
            motorDE_bg: '0',
            motorDE_accV: data.vibrationData.motor.de.acceleration,
            motorDE_accH: '0',
            motorDE_accAxl: '0',
            motorDE_velV: data.vibrationData.motor.de.velocity,
            motorDE_velH: '0',
            motorDE_velAxl: '0',
            motorDE_temp: data.vibrationData.motor.de.temperature
        };

        addVibrationHistoryEntry(vibrationRecord);
        triggerDataUpdate();

        toast({
            title: "Vibration Data Recorded",
            description: `Successfully recorded vibration data for ${selectedEquipment?.name}`,
            variant: "default",
        });

        onClose();
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent className="max-w-6xl h-[90vh] flex flex-col p-0">
                <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-blue-100">
                                <Waves className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                                <DialogTitle className="text-xl font-semibold">
                                    Enhanced Vibration Monitoring
                                </DialogTitle>
                                <DialogDescription className="text-sm text-muted-foreground">
                                    Professional vibration data collection and analysis
                                </DialogDescription>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                                Step {currentStep + 1} of {FORM_STEPS.length}
                            </Badge>
                        </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-4">
                        <div className="flex justify-between text-xs text-muted-foreground mb-2">
                            <span>Progress</span>
                            <span>{Math.round(formProgress)}%</span>
                        </div>
                        <Progress value={formProgress} className="h-2" />
                    </div>
                </DialogHeader>

                {/* Step Navigation */}
                <div className="px-6 py-3 border-b bg-gray-50/50">
                    <div className="flex items-center justify-between">
                        {FORM_STEPS.map((step, index) => {
                            const StepIcon = step.icon;
                            const isActive = index === currentStep;
                            const isCompleted = index < currentStep;

                            return (
                                <div key={step.id} className="flex items-center">
                                    <div className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all ${isActive
                                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                                        : isCompleted
                                            ? 'bg-green-100 text-green-700'
                                            : 'text-gray-500'
                                        }`}>
                                        <StepIcon className="h-4 w-4" />
                                        <span className="text-sm font-medium hidden md:block">{step.title}</span>
                                    </div>
                                    {index < FORM_STEPS.length - 1 && (
                                        <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Form Content */}
                <div className="flex-1 overflow-y-auto">
                    <form onSubmit={handleSubmit(onSubmit)} className="h-full">
                        {/* Step 1: Equipment Selection */}
                        {currentStep === 0 && (
                            <div className="p-6 space-y-6">
                                <div className="text-center mb-6">
                                    <h3 className="text-lg font-semibold mb-2">Select Equipment for Monitoring</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Choose the equipment you want to monitor from our hierarchical asset database
                                    </p>
                                </div>

                                {/* Search and Filter */}
                                <div className="flex gap-4 mb-6">
                                    <div className="flex-1 relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search equipment by name, manufacturer, or asset tag..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                    <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                                        <SelectTrigger className="w-48">
                                            <Filter className="h-4 w-4 mr-2" />
                                            <SelectValue placeholder="Filter by category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Categories</SelectItem>
                                            {Object.entries(EQUIPMENT_CATEGORIES).map(([key, category]) => (
                                                <SelectItem key={key} value={key}>
                                                    {category.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Equipment Grid */}
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                                    {filteredEquipment.map((equipment) => {
                                        const isSelected = selectedEquipment?.id === equipment.id;
                                        const CategoryIcon = equipment.categoryInfo?.icon || Factory;

                                        return (
                                            <Card
                                                key={equipment.id}
                                                className={`cursor-pointer transition-all hover:shadow-md ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                                                    }`}
                                                onClick={() => handleEquipmentSelect(equipment.id)}
                                            >
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <div className={`p-2 rounded-lg ${equipment.categoryInfo?.color || 'bg-gray-500'} bg-opacity-10`}>
                                                                <CategoryIcon className="h-4 w-4" />
                                                            </div>
                                                            <div>
                                                                <CardTitle className="text-sm font-medium line-clamp-1">
                                                                    {equipment.name}
                                                                </CardTitle>
                                                                <p className="text-xs text-muted-foreground">
                                                                    {equipment.assetTag}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        {isSelected && (
                                                            <CheckCircle className="h-5 w-5 text-blue-500" />
                                                        )}
                                                    </div>
                                                </CardHeader>
                                                <CardContent className="pt-0">
                                                    <div className="space-y-2">
                                                        <div className="flex items-center gap-2 text-xs">
                                                            <Factory className="h-3 w-3 text-gray-400" />
                                                            <span>{equipment.manufacturer}</span>
                                                        </div>
                                                        <div className="flex items-center gap-2 text-xs">
                                                            <MapPin className="h-3 w-3 text-gray-400" />
                                                            <span className="line-clamp-1">{equipment.hierarchicalPath}</span>
                                                        </div>
                                                        <Badge variant="outline" className="text-xs">
                                                            {equipment.categoryInfo?.label}
                                                        </Badge>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </div>

                                {/* Selected Equipment Details */}
                                {selectedEquipment && (
                                    <Card className="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <CheckCircle className="h-5 w-5 text-green-500" />
                                                Selected Equipment Details
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Equipment Name</Label>
                                                    <p className="text-sm font-medium">{selectedEquipment.name}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Manufacturer</Label>
                                                    <p className="text-sm">{selectedEquipment.manufacturer}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Model</Label>
                                                    <p className="text-sm">{selectedEquipment.model}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Asset Tag</Label>
                                                    <p className="text-sm font-mono">{selectedEquipment.assetTag}</p>
                                                </div>
                                                <div className="col-span-2">
                                                    <Label className="text-xs font-medium text-gray-600">Location</Label>
                                                    <p className="text-sm">{selectedEquipment.hierarchicalPath}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Category</Label>
                                                    <Badge variant="outline">{selectedEquipment.categoryInfo?.label}</Badge>
                                                </div>
                                                <div>
                                                    <Label className="text-xs font-medium text-gray-600">Serial Number</Label>
                                                    <p className="text-sm font-mono">{selectedEquipment.serialNumber}</p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        )}

                        {/* Step 2: Operational Data */}
                        {currentStep === 1 && (
                            <div className="p-6 space-y-6">
                                <div className="text-center mb-6">
                                    <h3 className="text-lg font-semibold mb-2">Operational Parameters</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Enter current operational conditions for {selectedEquipment?.name}
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Date and Time */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <CalendarIcon className="h-4 w-4" />
                                                Measurement Date & Time
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <Controller
                                                name="date"
                                                control={control}
                                                rules={{ required: "Date is required" }}
                                                render={({ field }) => (
                                                    <div>
                                                        <Label>Measurement Date</Label>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <Button variant="outline" className="w-full justify-start text-left font-normal">
                                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                                    {field.value ? format(new Date(field.value), 'PPP') : 'Select date'}
                                                                </Button>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0">
                                                                <Calendar
                                                                    mode="single"
                                                                    selected={field.value ? new Date(field.value) : undefined}
                                                                    onSelect={(date) => field.onChange(date ? format(date, 'yyyy-MM-dd') : '')}
                                                                    initialFocus
                                                                />
                                                            </PopoverContent>
                                                        </Popover>
                                                        {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date.message}</p>}
                                                    </div>
                                                )}
                                            />
                                        </CardContent>
                                    </Card>

                                    {/* Operating Conditions */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <Gauge className="h-4 w-4" />
                                                Operating Conditions
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <Controller
                                                    name="operatingHours"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Operating Hours</Label>
                                                            <Input {...field} placeholder="8760" type="number" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingPower"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Power (kW)</Label>
                                                            <Input {...field} placeholder="75" type="number" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingSpeed"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Speed (RPM)</Label>
                                                            <Input {...field} placeholder="1450" type="number" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingTemperature"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Temperature (°C)</Label>
                                                            <Input {...field} placeholder="45" type="number" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingPressure"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Pressure (bar)</Label>
                                                            <Input {...field} placeholder="5.5" type="number" />
                                                        </div>
                                                    )}
                                                />
                                                <Controller
                                                    name="operatingFlow"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <div>
                                                            <Label>Flow Rate (m³/h)</Label>
                                                            <Input {...field} placeholder="100" type="number" />
                                                        </div>
                                                    )}
                                                />
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>

                                {/* Equipment Specifications Reference */}
                                {selectedEquipment?.specifications && (
                                    <Card className="bg-blue-50 border-blue-200">
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2 text-base">
                                                <Database className="h-4 w-4" />
                                                Equipment Specifications (Reference)
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                                {selectedEquipment.specifications.ratedPower && (
                                                    <div>
                                                        <Label className="text-xs text-gray-600">Rated Power</Label>
                                                        <p className="font-medium">{selectedEquipment.specifications.ratedPower} kW</p>
                                                    </div>
                                                )}
                                                {selectedEquipment.specifications.flowRate && (
                                                    <div>
                                                        <Label className="text-xs text-gray-600">Design Flow</Label>
                                                        <p className="font-medium">{selectedEquipment.specifications.flowRate} m³/h</p>
                                                    </div>
                                                )}
                                                {selectedEquipment.specifications.head && (
                                                    <div>
                                                        <Label className="text-xs text-gray-600">Design Head</Label>
                                                        <p className="font-medium">{selectedEquipment.specifications.head} m</p>
                                                    </div>
                                                )}
                                                {selectedEquipment.specifications.pressure && (
                                                    <div>
                                                        <Label className="text-xs text-gray-600">Design Pressure</Label>
                                                        <p className="font-medium">{selectedEquipment.specifications.pressure} bar</p>
                                                    </div>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>
                        )}

                        {/* Step 3: Vibration Measurements */}
                        {currentStep === 2 && (
                            <div className="p-6 space-y-6">
                                <div className="text-center mb-6">
                                    <h3 className="text-lg font-semibold mb-2">Vibration Measurements</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Record vibration readings for {selectedEquipment?.name}
                                    </p>
                                </div>

                                {/* ISO 10816 Guidelines */}
                                <Card className="bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 text-base">
                                            <Shield className="h-4 w-4" />
                                            ISO 10816 Vibration Guidelines
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-green-500 rounded"></div>
                                                <span className="text-sm">Good: &lt; {selectedEquipment?.categoryInfo?.vibrationLimits?.good || 2.8} mm/s</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                                                <span className="text-sm">Acceptable: &lt; {selectedEquipment?.categoryInfo?.vibrationLimits?.acceptable || 7.1} mm/s</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-red-500 rounded"></div>
                                                <span className="text-sm">Unacceptable: &gt; {selectedEquipment?.categoryInfo?.vibrationLimits?.unacceptable || 18.0} mm/s</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Vibration Measurement Cards */}
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* Pump Measurements */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <Cpu className="h-4 w-4 text-blue-500" />
                                                Pump Measurements
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            {/* Non-Drive End */}
                                            <div>
                                                <Label className="text-sm font-medium mb-2 block">Non-Drive End (NDE)</Label>
                                                <div className="grid grid-cols-2 gap-3">
                                                    <Controller
                                                        name="vibrationData.pump.nde.velocity"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Velocity (mm/s)</Label>
                                                                <Input {...field} placeholder="2.5" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.nde.acceleration"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Acceleration (m/s²)</Label>
                                                                <Input {...field} placeholder="5.0" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.nde.displacement"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Displacement (μm)</Label>
                                                                <Input {...field} placeholder="25" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.nde.temperature"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Temperature (°C)</Label>
                                                                <Input {...field} placeholder="45" type="number" />
                                                            </div>
                                                        )}
                                                    />
                                                </div>
                                            </div>

                                            <Separator />

                                            {/* Drive End */}
                                            <div>
                                                <Label className="text-sm font-medium mb-2 block">Drive End (DE)</Label>
                                                <div className="grid grid-cols-2 gap-3">
                                                    <Controller
                                                        name="vibrationData.pump.de.velocity"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Velocity (mm/s)</Label>
                                                                <Input {...field} placeholder="2.8" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.de.acceleration"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Acceleration (m/s²)</Label>
                                                                <Input {...field} placeholder="5.5" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.de.displacement"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Displacement (μm)</Label>
                                                                <Input {...field} placeholder="28" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.pump.de.temperature"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Temperature (°C)</Label>
                                                                <Input {...field} placeholder="47" type="number" />
                                                            </div>
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Motor Measurements */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <Zap className="h-4 w-4 text-green-500" />
                                                Motor Measurements
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            {/* Non-Drive End */}
                                            <div>
                                                <Label className="text-sm font-medium mb-2 block">Non-Drive End (NDE)</Label>
                                                <div className="grid grid-cols-2 gap-3">
                                                    <Controller
                                                        name="vibrationData.motor.nde.velocity"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Velocity (mm/s)</Label>
                                                                <Input {...field} placeholder="1.8" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.nde.acceleration"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Acceleration (m/s²)</Label>
                                                                <Input {...field} placeholder="3.5" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.nde.displacement"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Displacement (μm)</Label>
                                                                <Input {...field} placeholder="18" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.nde.temperature"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Temperature (°C)</Label>
                                                                <Input {...field} placeholder="42" type="number" />
                                                            </div>
                                                        )}
                                                    />
                                                </div>
                                            </div>

                                            <Separator />

                                            {/* Drive End */}
                                            <div>
                                                <Label className="text-sm font-medium mb-2 block">Drive End (DE)</Label>
                                                <div className="grid grid-cols-2 gap-3">
                                                    <Controller
                                                        name="vibrationData.motor.de.velocity"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Velocity (mm/s)</Label>
                                                                <Input {...field} placeholder="1.9" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.de.acceleration"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Acceleration (m/s²)</Label>
                                                                <Input {...field} placeholder="3.8" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.de.displacement"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Displacement (μm)</Label>
                                                                <Input {...field} placeholder="19" type="number" step="0.1" />
                                                            </div>
                                                        )}
                                                    />
                                                    <Controller
                                                        name="vibrationData.motor.de.temperature"
                                                        control={control}
                                                        render={({ field }) => (
                                                            <div>
                                                                <Label className="text-xs">Temperature (°C)</Label>
                                                                <Input {...field} placeholder="44" type="number" />
                                                            </div>
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        )}

                        {/* Step 4: Analysis & Review */}
                        {currentStep === 3 && (
                            <div className="p-6 space-y-6">
                                <div className="text-center mb-6">
                                    <h3 className="text-lg font-semibold mb-2">Analysis & Review</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Review measurements and add analysis notes
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {/* Summary Card */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <BarChart3 className="h-4 w-4" />
                                                Measurement Summary
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label className="text-xs text-gray-600">Equipment</Label>
                                                    <p className="text-sm font-medium">{selectedEquipment?.name}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs text-gray-600">Date</Label>
                                                    <p className="text-sm">{formValues.date}</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs text-gray-600">Pump Velocity (NDE)</Label>
                                                    <p className="text-sm">{formValues.vibrationData?.pump?.nde?.velocity || 'N/A'} mm/s</p>
                                                </div>
                                                <div>
                                                    <Label className="text-xs text-gray-600">Motor Velocity (NDE)</Label>
                                                    <p className="text-sm">{formValues.vibrationData?.motor?.nde?.velocity || 'N/A'} mm/s</p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    {/* Analysis Notes */}
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <FileText className="h-4 w-4" />
                                                Analysis & Notes
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                            <Controller
                                                name="overallCondition"
                                                control={control}
                                                render={({ field }) => (
                                                    <div>
                                                        <Label>Overall Condition</Label>
                                                        <Select value={field.value} onValueChange={field.onChange}>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select condition" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="good">Good</SelectItem>
                                                                <SelectItem value="acceptable">Acceptable</SelectItem>
                                                                <SelectItem value="unacceptable">Unacceptable</SelectItem>
                                                                <SelectItem value="critical">Critical</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                    </div>
                                                )}
                                            />
                                            <Controller
                                                name="recommendations"
                                                control={control}
                                                render={({ field }) => (
                                                    <div>
                                                        <Label>Recommendations</Label>
                                                        <textarea
                                                            {...field}
                                                            className="w-full p-2 border rounded-md resize-none"
                                                            rows={3}
                                                            placeholder="Enter maintenance recommendations..."
                                                        />
                                                    </div>
                                                )}
                                            />
                                            <Controller
                                                name="nextInspectionDate"
                                                control={control}
                                                render={({ field }) => (
                                                    <div>
                                                        <Label>Next Inspection Date</Label>
                                                        <Input {...field} type="date" />
                                                    </div>
                                                )}
                                            />
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        )}
                    </form>
                </div>

                {/* Footer with Navigation */}
                <div className="border-t bg-gray-50/50 px-6 py-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            {currentStep > 0 && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={prevStep}
                                    className="flex items-center gap-2"
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                    Previous
                                </Button>
                            )}
                        </div>

                        <div className="flex items-center gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onClose}
                            >
                                Cancel
                            </Button>

                            {currentStep < FORM_STEPS.length - 1 ? (
                                <Button
                                    type="button"
                                    onClick={nextStep}
                                    disabled={currentStep === 0 && !selectedEquipment}
                                    className="flex items-center gap-2"
                                >
                                    Next
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            ) : (
                                <Button
                                    type="submit"
                                    onClick={handleSubmit(onSubmit)}
                                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                                >
                                    <CheckCircle className="h-4 w-4" />
                                    Save Vibration Data
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default EnhancedVibrationForm;
