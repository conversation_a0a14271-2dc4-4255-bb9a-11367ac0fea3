import type { Equipment } from "@/types/eams";

// Helper function to create breadcrumbs
const createBreadcrumbs = (path: string) => {
  const parts = path.split('/');
  return parts.map((part, index) => ({
    id: `${part.toLowerCase().replace(/\s+/g, '-')}-${index}`,
    name: part,
    level: index === 0 ? 'zone' : index === 1 ? 'station' : index === 2 ? 'line' : 'equipment',
    path: parts.slice(0, index + 1).join('/')
  }));
};

// Priming System Equipment
export const primingSystemEquipment: Equipment[] = [
  {
    id: "EQ-A01-PS-PP1",
    name: "Priming Pump PP1",
    level: "equipment",
    parentId: "SYSTEM-A01-PS",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-PS",
    path: "Zone A/Pump Station A01/Priming System/Priming Pump PP1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Priming System/Priming Pump PP1"),
    type: "mechanical",
    category: "pump",
    manufacturer: "ROBUSCHI/Italy",
    model: "RVS14/M",
    serialNumber: "2008705",
    assetTag: "PP1-A01-PS",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Priming System",
      building: "Priming House",
      floor: "Ground Floor",
      room: "Priming Room 1"
    },
    specifications: {
      flowRate: 58, // m³/hr
      pressure: 33, // m.bar vacuum
      ratedPower: 15, // kW estimated
      efficiency: 80
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 8760, // Less operating hours as it's not continuous
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 1.9,
        peakVelocity: 3.4,
        displacement: 0.022,
        frequency: [30, 60, 90, 120],
        spectrum: [1.9, 1.4, 1.0, 0.6],
        iso10816Zone: "A",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Mohamed"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-PS-PP2",
    name: "Priming Pump PP2",
    level: "equipment",
    parentId: "SYSTEM-A01-PS",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-PS",
    path: "Zone A/Pump Station A01/Priming System/Priming Pump PP2",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Priming System/Priming Pump PP2"),
    type: "mechanical",
    category: "pump",
    manufacturer: "ROBUSCHI/Italy",
    model: "RVS14/M",
    serialNumber: "2008717",
    assetTag: "PP2-A01-PS",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Priming System",
      building: "Priming House",
      floor: "Ground Floor",
      room: "Priming Room 2"
    },
    specifications: {
      flowRate: 58, // m³/hr
      pressure: 33, // m.bar vacuum
      ratedPower: 15, // kW estimated
      efficiency: 80
    },
    status: "operational",
    condition: "excellent",
    installationDate: "2020-01-15",
    operatingHours: 8760,
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 1.7,
        peakVelocity: 3.1,
        displacement: 0.019,
        frequency: [30, 60, 90, 120],
        spectrum: [1.7, 1.3, 0.9, 0.5],
        iso10816Zone: "A",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Mohamed"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "excellent",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-PS-C1",
    name: "Compressor C1",
    level: "equipment",
    parentId: "SYSTEM-A01-PS",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-PS",
    path: "Zone A/Pump Station A01/Priming System/Compressor C1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Priming System/Compressor C1"),
    type: "mechanical",
    category: "compressor",
    manufacturer: "El Haggar Misr",
    model: "HGF 500/580",
    serialNumber: "C-A01-PS-001",
    assetTag: "C1-A01-PS",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Priming System",
      building: "Compressor House",
      floor: "Ground Floor",
      room: "Compressor Room 1"
    },
    specifications: {
      ratedPower: 37, // kW estimated
      pressure: 8, // bar
      flowRate: 500, // L/min estimated
      efficiency: 85
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 17520, // More operating hours
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 2.3,
        peakVelocity: 4.1,
        displacement: 0.028,
        frequency: [30, 60, 90, 120],
        spectrum: [2.3, 1.8, 1.3, 0.9],
        iso10816Zone: "B",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Mohamed"
      },
      thermography: {
        maxTemperature: 75,
        avgTemperature: 68,
        hotSpots: [],
        baseline: 65,
        deltaT: 10,
        measurementDate: "2024-12-15"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-PS-F1",
    name: "Filter F1",
    level: "equipment",
    parentId: "SYSTEM-A01-PS",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-PS",
    path: "Zone A/Pump Station A01/Priming System/Filter F1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Priming System/Filter F1"),
    type: "mechanical",
    category: "filter",
    manufacturer: "Placeholder",
    model: "Air Filter",
    serialNumber: "F-A01-PS-001",
    assetTag: "F1-A01-PS",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Priming System",
      building: "Filter House",
      floor: "Ground Floor",
      room: "Filter Room 1"
    },
    specifications: {
      diameter: 200, // mm
      pressure: 10, // bar
      efficiency: 99.5 // %
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  }
];

// Water Hammer System Equipment
export const waterHammerSystemEquipment: Equipment[] = [
  {
    id: "EQ-A01-WH-T1",
    name: "Water Hammer Tank T1",
    level: "equipment",
    parentId: "SYSTEM-A01-WH",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-WH",
    path: "Zone A/Pump Station A01/Water Hammer System/Water Hammer Tank T1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Water Hammer System/Water Hammer Tank T1"),
    type: "mechanical",
    category: "tank",
    manufacturer: "HC",
    model: "Water Hammer Tank",
    serialNumber: "T-A01-WH-001",
    assetTag: "T1-A01-WH",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Water Hammer System",
      building: "Tank House",
      floor: "Ground Floor",
      room: "Tank Room 1"
    },
    specifications: {
      volume: 35, // m³
      nozzleSize: 400, // mm
      pressure: 16, // bar
      quantity: 2
    },
    status: "operational",
    condition: "excellent",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "excellent",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-WH-V1",
    name: "Check Valve CV1",
    level: "equipment",
    parentId: "SYSTEM-A01-WH",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-WH",
    path: "Zone A/Pump Station A01/Water Hammer System/Check Valve CV1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Water Hammer System/Check Valve CV1"),
    type: "mechanical",
    category: "valve",
    manufacturer: "Placeholder",
    model: "Check Valve",
    serialNumber: "V-A01-WH-001",
    assetTag: "CV1-A01-WH",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Water Hammer System",
      building: "Valve House",
      floor: "Ground Floor",
      room: "Valve Room 1"
    },
    specifications: {
      diameter: 150, // mm
      pressure: 16, // bar
      temperature: 80 // °C
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-WH-C1",
    name: "Compressor WH-C1",
    level: "equipment",
    parentId: "SYSTEM-A01-WH",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-WH",
    path: "Zone A/Pump Station A01/Water Hammer System/Compressor WH-C1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Water Hammer System/Compressor WH-C1"),
    type: "mechanical",
    category: "compressor",
    manufacturer: "El Haggar Misr",
    model: "HGF 500/580",
    serialNumber: "C-A01-WH-001",
    assetTag: "WH-C1-A01-WH",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Water Hammer System",
      building: "Compressor House",
      floor: "Ground Floor",
      room: "WH Compressor Room 1"
    },
    specifications: {
      ratedPower: 37, // kW
      pressure: 8, // bar
      flowRate: 500, // L/min
      quantity: 3,
      efficiency: 85
    },
    status: "operational",
    condition: "good",
    installationDate: "2020-01-15",
    operatingHours: 26280, // 3 years × 8760 hours
    conditionMonitoring: {
      vibration: {
        rmsVelocity: 2.4,
        peakVelocity: 4.3,
        displacement: 0.029,
        frequency: [30, 60, 90, 120],
        spectrum: [2.4, 1.9, 1.4, 1.0],
        iso10816Zone: "B",
        measurementDate: "2024-12-15",
        measuredBy: "Technician Omar"
      },
      thermography: {
        maxTemperature: 78,
        avgTemperature: 71,
        hotSpots: [],
        baseline: 68,
        deltaT: 10,
        measurementDate: "2024-12-15"
      },
      lastUpdated: "2024-12-15",
      overallCondition: "good",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  },
  {
    id: "EQ-A01-WH-S1",
    name: "Pressure Sensor WH-S1",
    level: "equipment",
    parentId: "SYSTEM-A01-WH",
    zoneId: "ZONE-A",
    stationId: "STATION-A01",
    systemId: "SYSTEM-A01-WH",
    path: "Zone A/Pump Station A01/Water Hammer System/Pressure Sensor WH-S1",
    breadcrumbs: createBreadcrumbs("Zone A/Pump Station A01/Water Hammer System/Pressure Sensor WH-S1"),
    type: "instrumentation",
    category: "sensor",
    manufacturer: "Placeholder",
    model: "Pressure Sensor",
    serialNumber: "S-A01-WH-001",
    assetTag: "WH-S1-A01-WH",
    location: {
      zone: "Zone A",
      station: "Pump Station A01",
      system: "Water Hammer System",
      building: "Control House",
      floor: "Ground Floor",
      room: "WH Control Room"
    },
    specifications: {
      ratedVoltage: 24, // V
      frequency: 50, // Hz
      pressure: 25 // bar max
    },
    status: "operational",
    condition: "excellent",
    installationDate: "2020-01-15",
    operatingHours: 35040,
    conditionMonitoring: {
      lastUpdated: "2024-12-15",
      overallCondition: "excellent",
      alerts: []
    },
    failureHistory: [],
    maintenanceHistory: [],
    history: [],
    createdAt: "2020-01-15T00:00:00Z",
    updatedAt: "2024-12-15T00:00:00Z"
  }
];
